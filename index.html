<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Redefining Productivity</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="src/assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="src/assets/images/gpace-logo-white.png">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style">
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Application CSS -->
    <link rel="stylesheet" href="./src/css/main.css">
    <link rel="stylesheet" href="./src/css/sideDrawer.css">
    <link rel="stylesheet" href="./src/css/notification.css">
    
    <!-- ES Module Shims for older browsers -->
    <script async src="https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js"></script>
    
    <!-- Firebase -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        
        // Make Firebase available globally for compatibility
        window.firebaseModules = { initializeApp, getAuth, getFirestore };
    </script>
    
    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://kit.fontawesome.com/51198d7b97.js" crossorigin="anonymous"></script>
    
    <style>
        /* Loading screen styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #121212, #1e1e1e);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        .loading-text {
            color: #ffffff;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .loading-subtext {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(254, 44, 85, 0.3);
            border-top: 3px solid #fe2c55;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-top: 20px;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* App container styles */
        #root {
            min-height: 100vh;
            background-color: #121212;
            color: #ffffff;
        }
        
        .page-container {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .page-container.loaded {
            opacity: 1;
        }
        
        /* Error page styles */
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 20px;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            background: linear-gradient(45deg, #fe2c55, #25f4ee);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .error-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #fe2c55, #25f4ee);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <img src="src/assets/images/gpace-logo-white.png" alt="GPAce Logo" class="loading-logo">
        <div class="loading-text">GPAce</div>
        <div class="loading-subtext">Redefining Productivity</div>
        <div class="loading-spinner"></div>
    </div>
    
    <!-- Main Application Container -->
    <div id="root">
        <!-- Dynamic content will be loaded here -->
    </div>
    
    <!-- Router and Application Entry Point -->
    <script type="module" src="./src/js/app.js"></script>
    
    <!-- Router Script -->
    <script type="module">
        // Simple client-side router
        class GPAceRouter {
            constructor() {
                this.routes = new Map();
                this.currentRoute = null;
                this.setupEventListeners();
                this.setupDefaultRoutes();
            }
            
            setupDefaultRoutes() {
                // Define application routes
                this.addRoute('/', () => this.loadPage('landing'));
                this.addRoute('/landing', () => this.loadPage('landing'));
                this.addRoute('/grind', () => this.loadPage('grind'));
                this.addRoute('/tasks', () => this.loadPage('tasks'));
                this.addRoute('/workspace', () => this.loadPage('workspace'));
                this.addRoute('/flashcards', () => this.loadPage('flashcards'));
                this.addRoute('/academic-details', () => this.loadPage('academic-details'));
                this.addRoute('/study-spaces', () => this.loadPage('study-spaces'));
                this.addRoute('/daily-calendar', () => this.loadPage('daily-calendar'));
                this.addRoute('/instant-test-feedback', () => this.loadPage('instant-test-feedback'));
                this.addRoute('/extracted', () => this.loadPage('extracted'));
                this.addRoute('/subject-marks', () => this.loadPage('subject-marks'));
                this.addRoute('/settings', () => this.loadPage('settings'));
                this.addRoute('/priority-calculator', () => this.loadPage('priority-calculator'));
                this.addRoute('/priority-list', () => this.loadPage('priority-list'));
                this.addRoute('/sleep-saboteurs', () => this.loadPage('sleep-saboteurs'));
                this.addRoute('/404', () => this.loadErrorPage(404));
            }
            
            addRoute(path, handler) {
                this.routes.set(path, handler);
            }
            
            setupEventListeners() {
                // Handle browser back/forward buttons
                window.addEventListener('popstate', () => {
                    this.handleRoute();
                });
                
                // Handle link clicks
                document.addEventListener('click', (e) => {
                    if (e.target.matches('a[href^="/"], a[href^="#/"]')) {
                        e.preventDefault();
                        const href = e.target.getAttribute('href').replace('#', '');
                        this.navigate(href);
                    }
                });
            }
            
            navigate(path) {
                if (path !== this.currentRoute) {
                    history.pushState(null, '', path);
                    this.handleRoute();
                }
            }
            
            handleRoute() {
                const path = window.location.pathname;
                const handler = this.routes.get(path) || this.routes.get('/404');
                
                if (handler) {
                    this.currentRoute = path;
                    handler();
                } else {
                    this.navigate('/404');
                }
            }
            
            async loadPage(pageName) {
                try {
                    console.log(`Loading page: ${pageName}`);
                    
                    // Show loading if not initial load
                    if (this.currentRoute) {
                        this.showLoading();
                    }
                    
                    // Load page content
                    const response = await fetch(`./src/pages/${pageName}.html`);
                    if (!response.ok) {
                        throw new Error(`Failed to load page: ${response.status}`);
                    }
                    
                    const html = await response.text();
                    
                    // Extract body content (remove html, head, body tags)
                    const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
                    const content = bodyMatch ? bodyMatch[1] : html;
                    
                    // Update page content
                    document.getElementById('root').innerHTML = content;
                    
                    // Update page title
                    const titleMatch = html.match(/<title[^>]*>([\s\S]*?)<\/title>/i);
                    if (titleMatch) {
                        document.title = titleMatch[1];
                    }
                    
                    // Load page-specific CSS
                    await this.loadPageCSS(pageName);
                    
                    // Initialize page-specific modules
                    await this.initPageModules(pageName);
                    
                    // Hide loading screen
                    this.hideLoading();
                    
                    // Trigger page loaded event
                    window.dispatchEvent(new CustomEvent('pageLoaded', { detail: { page: pageName } }));
                    
                } catch (error) {
                    console.error('Error loading page:', error);
                    this.loadErrorPage(500);
                }
            }
            
            async loadPageCSS(pageName) {
                // Load page-specific CSS if it exists
                const cssFiles = {
                    'grind': ['grind.css', 'task-display.css', 'text-expansion.css', 'ai-search-response.css', 'task-notes.css'],
                    'flashcards': ['flashcards.css'],
                    'academic-details': ['academic-details.css'],
                    'workspace': ['workspace.css'],
                    'study-spaces': ['study-spaces.css'],
                    'daily-calendar': ['daily-calendar.css'],
                    'extracted': ['extracted.css'],
                    'subject-marks': ['subject-marks.css'],
                    'settings': ['settings.css'],
                    'priority-calculator': ['priority-calculator.css'],
                    'sleep-saboteurs': ['sleep-saboteurs.css'],
                    'instant-test-feedback': ['test-feedback.css']
                };
                
                const files = cssFiles[pageName] || [];
                
                for (const file of files) {
                    if (!document.querySelector(`link[href*="${file}"]`)) {
                        const link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = `./src/css/${file}`;
                        document.head.appendChild(link);
                    }
                }
            }
            
            async initPageModules(pageName) {
                // Initialize page-specific functionality through the main app
                if (window.GPAceApp && window.GPAceApp.initPageModules) {
                    await window.GPAceApp.initPageModules(pageName);
                }
            }
            
            loadErrorPage(code) {
                const errorMessages = {
                    404: 'Page Not Found',
                    500: 'Internal Server Error'
                };
                
                const message = errorMessages[code] || 'Unknown Error';
                
                document.getElementById('root').innerHTML = `
                    <div class="error-container">
                        <div class="error-code">${code}</div>
                        <div class="error-message">${message}</div>
                        <div class="error-actions">
                            <a href="/" class="btn btn-primary">Go Home</a>
                            <a href="/grind" class="btn btn-primary">Grind Mode</a>
                        </div>
                    </div>
                `;
                
                this.hideLoading();
            }
            
            showLoading() {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.classList.remove('hidden');
                }
            }
            
            hideLoading() {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    setTimeout(() => {
                        loadingScreen.classList.add('hidden');
                    }, 500);
                }
            }
            
            init() {
                // Handle initial route
                this.handleRoute();
            }
        }
        
        // Initialize router when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.router = new GPAceRouter();
            window.router.init();
        });
        
        // Make router available globally
        window.navigate = (path) => window.router?.navigate(path);
    </script>
</body>
</html>
