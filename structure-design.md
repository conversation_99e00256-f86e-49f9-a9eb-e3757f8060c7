# GPAce Structure Design

## Current State Analysis
Based on the audit, we have 162 files scattered across multiple directories with unclear separation of concerns. The largest files indicate major functional areas that need proper organization.

## Proposed Structure

```
gpace/
├─ index.html                    ← Single bootstrapper/router
├─ src/
│  ├─ pages/                     ← Static HTML fragments (views)
│  │  ├─ grind.html             ← Main study interface (201KB → fragment)
│  │  ├─ landing.html           ← Landing page (55KB → fragment)
│  │  ├─ academic-details.html  ← Academic management
│  │  ├─ tasks.html             ← Task management
│  │  ├─ workspace.html         ← Workspace interface
│  │  ├─ study-spaces.html      ← Study space management
│  │  ├─ test-feedback.html     ← Test feedback system
│  │  ├─ extracted.html         ← Content extraction
│  │  └─ 404.html               ← Error page
│  │
│  ├─ components/               ← Reusable UI pieces
│  │  ├─ flashcards/
│  │  │  ├─ flashcard.html
│  │  │  ├─ flashcard.css
│  │  │  └─ flashcard.js
│  │  ├─ calendar/
│  │  │  ├─ daily-calendar.html
│  │  │  ├─ calendar.css
│  │  │  └─ calendar.js
│  │  ├─ navigation/
│  │  │  ├─ side-drawer.html
│  │  │  ├─ side-drawer.css
│  │  │  └─ side-drawer.js
│  │  ├─ priority/
│  │  │  ├─ priority-calculator.html
│  │  │  ├─ priority-calculator.css
│  │  │  └─ priority-calculator.js
│  │  ├─ settings/
│  │  │  ├─ settings.html
│  │  │  ├─ settings.css
│  │  │  └─ settings.js
│  │  └─ common/
│  │     ├─ header.html
│  │     ├─ footer.html
│  │     └─ modals.html
│  │
│  ├─ css/                      ← All styles organized by purpose
│  │  ├─ base/
│  │  │  ├─ reset.css
│  │  │  ├─ variables.css
│  │  │  ├─ typography.css
│  │  │  └─ layout.css
│  │  ├─ components/
│  │  │  ├─ buttons.css
│  │  │  ├─ forms.css
│  │  │  ├─ cards.css
│  │  │  ├─ modals.css
│  │  │  └─ navigation.css
│  │  ├─ pages/
│  │  │  ├─ grind.css          ← Split from 126KB monolith
│  │  │  ├─ landing.css
│  │  │  ├─ workspace.css
│  │  │  └─ study-spaces.css
│  │  ├─ themes/
│  │  │  ├─ light.css
│  │  │  ├─ dark.css
│  │  │  └─ compact.css
│  │  └─ main.css              ← Import orchestrator
│  │
│  ├─ js/
│  │  ├─ components/           ← UI component logic
│  │  │  ├─ flashcard-manager.js
│  │  │  ├─ calendar-views.js
│  │  │  ├─ side-drawer.js
│  │  │  ├─ priority-calculator.js
│  │  │  ├─ task-display.js
│  │  │  ├─ workspace-editor.js
│  │  │  └─ notification-system.js
│  │  │
│  │  ├─ features/             ← Major feature modules
│  │  │  ├─ ai/
│  │  │  │  ├─ ai-researcher.js      ← Split from 101KB
│  │  │  │  ├─ speech-recognition.js ← Split from 81KB
│  │  │  │  ├─ speech-synthesis.js
│  │  │  │  └─ ai-latex-conversion.js
│  │  │  ├─ academic/
│  │  │  │  ├─ semester-management.js ← Split from 62KB
│  │  │  │  ├─ subject-management.js
│  │  │  │  ├─ marks-tracking.js
│  │  │  │  └─ timetable-analyzer.js
│  │  │  ├─ study/
│  │  │  │  ├─ study-spaces-manager.js ← Split from 57KB
│  │  │  │  ├─ pomodoro-timer.js
│  │  │  │  ├─ test-feedback.js      ← Split from 53KB
│  │  │  │  └─ sleep-schedule.js
│  │  │  ├─ tasks/
│  │  │  │  ├─ task-manager.js
│  │  │  │  ├─ task-filters.js
│  │  │  │  ├─ task-attachments.js
│  │  │  │  └─ priority-system.js
│  │  │  └─ workspace/
│  │  │     ├─ workspace-core.js
│  │  │     ├─ workspace-formatting.js
│  │  │     ├─ workspace-media.js
│  │  │     └─ workspace-tables.js
│  │  │
│  │  ├─ utils/                ← Helpers & APIs
│  │  │  ├─ api/
│  │  │  │  ├─ firebase-config.js
│  │  │  │  ├─ firestore.js
│  │  │  │  ├─ google-drive-api.js   ← Split from 72KB
│  │  │  │  ├─ gemini-api.js
│  │  │  │  └─ todoist-integration.js
│  │  │  ├─ storage/
│  │  │  │  ├─ indexed-db.js
│  │  │  │  ├─ storage-manager.js
│  │  │  │  └─ cache-manager.js
│  │  │  ├─ helpers/
│  │  │  │  ├─ date-utils.js
│  │  │  │  ├─ string-utils.js
│  │  │  │  ├─ validation.js
│  │  │  │  └─ formatters.js
│  │  │  └─ services/
│  │  │     ├─ alarm-service.js
│  │  │     ├─ notification-service.js
│  │  │     ├─ sync-service.js
│  │  │     └─ theme-manager.js
│  │  │
│  │  └─ app.js                ← Main entrypoint & router
│  │
│  ├─ assets/                  ← Static resources
│  │  ├─ images/
│  │  │  ├─ icons/
│  │  │  ├─ backgrounds/
│  │  │  └─ ui/
│  │  ├─ audio/
│  │  │  ├─ alarms/
│  │  │  │  ├─ alarm1.mp3
│  │  │  │  ├─ alarm2.mp3
│  │  │  │  └─ alarm3.mp3
│  │  │  └─ notifications/
│  │  │     ├─ notification.mp3
│  │  │     └─ pop.mp3
│  │  └─ fonts/
│  │     └─ custom-fonts/
│  │
│  └─ data/                    ← Static data & configs
│     ├─ locations.json
│     ├─ schedule.json
│     ├─ timetable.json
│     └─ defaults/
│
├─ public/                     ← Build output & service worker
│  ├─ service-worker.js
│  └─ manifest.json
│
├─ server/                     ← Backend (if needed)
│  ├─ server.js
│  ├─ routes/
│  └─ data-storage.js
│
└─ config/                     ← Build & deployment configs
   ├─ firebase.json
   ├─ package.json
   └─ build-scripts/
```

## Key Design Principles

### 1. Single Responsibility
- Each file has one clear purpose
- Large monoliths split into focused modules
- Clear separation between UI, logic, and data

### 2. Feature-Based Organization
- Related functionality grouped together
- AI features in `/features/ai/`
- Academic features in `/features/academic/`
- Study features in `/features/study/`

### 3. Component Architecture
- Reusable UI pieces in `/components/`
- Each component has its own HTML, CSS, and JS
- Promotes modularity and reusability

### 4. Utility Separation
- APIs and external integrations in `/utils/api/`
- Storage and caching in `/utils/storage/`
- Helper functions in `/utils/helpers/`
- Services in `/utils/services/`

### 5. Asset Management
- All static resources in `/assets/`
- Organized by type and purpose
- Easy to reference and manage

## Migration Strategy

### Phase 1: Structure Creation
1. Create directory structure
2. Move static assets first (safest)
3. Migrate CSS files with path updates

### Phase 2: Component Extraction
1. Extract reusable components
2. Split large CSS files (grind.css → multiple files)
3. Update component references

### Phase 3: JavaScript Refactoring
1. Split large JS files (>50KB)
2. Organize by feature areas
3. Create proper module imports/exports

### Phase 4: Integration
1. Create main app.js router
2. Update index.html as bootstrapper
3. Test all functionality

## Benefits of This Structure

1. **Maintainability**: Clear file organization
2. **Scalability**: Easy to add new features
3. **Reusability**: Component-based architecture
4. **Performance**: Smaller, focused files
5. **Developer Experience**: Intuitive file locations
6. **Build Optimization**: Ready for bundling tools
