/**
 * GPAce Application Entry Point
 * Main module that orchestrates the application initialization
 */

// Import utility modules
import { toggleTheme, initializeTheme, setupPeriodicSync } from './utils/ui-utilities.js';
import storageManager from './utils/storageManager.js';
import { initAlarmService } from './utils/alarm-service.js';
import { initFirebaseConfig } from './utils/firebase-config.js';

// Import core functionality
import { initSideDrawer } from './sideDrawer.js';
import { initCommonHeader } from './common-header.js';
import { initCrossTabSync } from './cross-tab-sync.js';

// Application state
class GPAceApp {
    constructor() {
        this.initialized = false;
        this.modules = new Map();
        this.storage = storageManager;
    }

    /**
     * Initialize the application
     */
    async init() {
        if (this.initialized) {
            console.warn('GPAce app already initialized');
            return;
        }

        console.log('🚀 Initializing GPAce Application...');

        try {
            // Initialize core utilities
            await this.initCore();
            
            // Initialize UI components
            await this.initUI();
            
            // Initialize data and services
            await this.initServices();
            
            // Initialize page-specific modules
            await this.initPageModules();
            
            this.initialized = true;
            console.log('✅ GPAce Application initialized successfully');
            
            // Dispatch custom event for other modules
            window.dispatchEvent(new CustomEvent('gpace:initialized'));
            
        } catch (error) {
            console.error('❌ Failed to initialize GPAce Application:', error);
            throw error;
        }
    }

    /**
     * Initialize core utilities and theme
     */
    async initCore() {
        console.log('🔧 Initializing core utilities...');
        
        // Initialize theme
        initializeTheme();
        
        // Setup periodic sync
        setupPeriodicSync();
        
        // Initialize Firebase if available
        try {
            await initFirebaseConfig();
        } catch (error) {
            console.warn('Firebase initialization failed:', error);
        }
    }

    /**
     * Initialize UI components
     */
    async initUI() {
        console.log('🎨 Initializing UI components...');
        
        // Initialize side drawer
        try {
            initSideDrawer();
        } catch (error) {
            console.warn('Side drawer initialization failed:', error);
        }

        // Initialize common header
        try {
            initCommonHeader();
        } catch (error) {
            console.warn('Common header initialization failed:', error);
        }

        // Initialize cross-tab sync
        try {
            initCrossTabSync();
        } catch (error) {
            console.warn('Cross-tab sync initialization failed:', error);
        }
    }

    /**
     * Initialize services
     */
    async initServices() {
        console.log('⚙️ Initializing services...');
        
        // Initialize alarm service
        try {
            await initAlarmService();
        } catch (error) {
            console.warn('Alarm service initialization failed:', error);
        }
        
        // Ensure data is initialized
        await this.ensureDataInitialized();
    }

    /**
     * Initialize page-specific modules based on current page
     */
    async initPageModules() {
        const currentPage = this.getCurrentPage();
        console.log(`📄 Initializing modules for page: ${currentPage}`);
        
        switch (currentPage) {
            case 'grind':
                await this.loadGrindModules();
                break;
            case 'tasks':
                await this.loadTaskModules();
                break;
            case 'workspace':
                await this.loadWorkspaceModules();
                break;
            case 'flashcards':
                await this.loadFlashcardModules();
                break;
            case 'academic-details':
                await this.loadAcademicModules();
                break;
            case 'study-spaces':
                await this.loadStudySpaceModules();
                break;
            default:
                console.log('No specific modules for this page');
        }
    }

    /**
     * Get current page name from URL or document
     */
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().replace('.html', '');
        return filename || 'index';
    }

    /**
     * Ensure data is initialized
     */
    async ensureDataInitialized() {
        console.log('🔄 Checking data initialization...');
        
        const dataInitialized = this.storage.getItem('dataInitialized') === 'true';
        const subjects = this.storage.getItem('academicSubjects', []);
        
        if (!dataInitialized || subjects.length === 0) {
            console.log('📥 Data not initialized, starting initialization...');
            if (typeof window.initializeFirestoreData === 'function') {
                await window.initializeFirestoreData();
            }
        } else {
            console.log('✅ Data already initialized');
            
            // Still update priority tasks to ensure they're current
            if (typeof window.updatePriorityTasks === 'function') {
                await window.updatePriorityTasks();
            }
        }
    }

    /**
     * Load modules for grind page
     */
    async loadGrindModules() {
        console.log('Loading grind page modules...');
        // Dynamic imports for grind-specific modules
        // These will be implemented as we convert more modules
    }

    /**
     * Load modules for task page
     */
    async loadTaskModules() {
        console.log('Loading task page modules...');
        // Dynamic imports for task-specific modules
    }

    /**
     * Load modules for workspace page
     */
    async loadWorkspaceModules() {
        console.log('Loading workspace page modules...');
        // Dynamic imports for workspace-specific modules
    }

    /**
     * Load modules for flashcard page
     */
    async loadFlashcardModules() {
        console.log('Loading flashcard page modules...');
        // Dynamic imports for flashcard-specific modules
    }

    /**
     * Load modules for academic details page
     */
    async loadAcademicModules() {
        console.log('Loading academic page modules...');
        // Dynamic imports for academic-specific modules
    }

    /**
     * Load modules for study spaces page
     */
    async loadStudySpaceModules() {
        console.log('Loading study space page modules...');
        // Dynamic imports for study space-specific modules
    }

    /**
     * Register a module
     */
    registerModule(name, module) {
        this.modules.set(name, module);
        console.log(`📦 Module registered: ${name}`);
    }

    /**
     * Get a registered module
     */
    getModule(name) {
        return this.modules.get(name);
    }
}

// Create global app instance
const app = new GPAceApp();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await app.init();
    } catch (error) {
        console.error('Failed to initialize application:', error);
    }
});

// Listen for storage changes
window.addEventListener('storage', async (e) => {
    if (e.key === 'academicSubjects' || e.key?.startsWith('tasks-')) {
        console.log('🔄 Storage changed, updating data...');
        await app.ensureDataInitialized();
    }
});

// Export app instance for global access
window.GPAceApp = app;
export default app;
